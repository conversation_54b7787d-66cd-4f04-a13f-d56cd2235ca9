/**
 * Field Type Compatibility Utilities
 *
 * Provides utilities for checking field type compatibility between
 * AutoPatient and CliniCore platforms. Determines whether fields
 * with different types can be safely mapped together for value
 * synchronization.
 *
 * @fileoverview Field type compatibility checking utilities
 * @version 2.0.0
 * @since 2024-07-28
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { getRequestId } from "@/utils/getRequestId";
import { findExistingCustomField } from "../matching/strategies";
import type { Platform } from "../types";

/**
 * Check if two field types are compatible for mapping
 *
 * Determines whether fields with different types can be safely mapped together
 * for value synchronization. Compatible types allow direct value conversion,
 * while incompatible types would require fallback conversions.
 *
 * @param sourceField - Source field to check compatibility for
 * @param targetField - Target field to check compatibility against
 * @param sourcePlatform - Platform of the source field ("ap" | "cc")
 * @returns True if field types are compatible for mapping, false otherwise
 *
 * @example
 * ```typescript
 * const ccSelectField = { type: "select", allowMultipleValues: false };
 * const apRadioField = { dataType: "RADIO" };
 * const apTextField = { dataType: "TEXT" };
 *
 * console.log(areFieldTypesCompatible(ccSelectField, apRadioField, "cc")); // true
 * console.log(areFieldTypesCompatible(ccSelectField, apTextField, "cc")); // false
 * ```
 *
 * @since 2.0.0
 */
export function areFieldTypesCompatible(
	sourceField: APGetCustomFieldType | GetCCCustomField,
	targetField: APGetCustomFieldType | GetCCCustomField,
	sourcePlatform: Platform,
): boolean {
	if (sourcePlatform === "cc") {
		const ccField = sourceField as GetCCCustomField;
		const apField = targetField as APGetCustomFieldType;
		return isCcToApTypeCompatible(ccField, apField);
	} else {
		const apField = sourceField as APGetCustomFieldType;
		const ccField = targetField as GetCCCustomField;
		return isApToCcTypeCompatible(apField, ccField);
	}
}

/**
 * Check if CC field type is compatible with AP field type
 *
 * @param ccField - CliniCore field to check
 * @param apField - AutoPatient field to check against
 * @returns True if types are compatible
 */
function isCcToApTypeCompatible(
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
): boolean {
	const ccType = ccField.type;
	const apType = apField.dataType;
	const allowMultiple = ccField.allowMultipleValues;

	// Direct type mappings
	const compatibilityMap: Record<string, string[]> = {
		text: ["TEXT", "LARGE_TEXT", "EMAIL", "PHONE"],
		textarea: ["LARGE_TEXT", "TEXT"],
		number: ["NUMERICAL", "MONETORY"],
		telephone: ["PHONE", "TEXT"],
		email: ["EMAIL", "TEXT"],
		date: ["DATE"],
		boolean: ["RADIO"], // Only for Yes/No radio fields
		select: allowMultiple
			? ["MULTIPLE_OPTIONS", "CHECKBOX", "TEXTBOX_LIST"]
			: ["SINGLE_OPTIONS", "RADIO"],
		"select-or-custom": allowMultiple
			? ["MULTIPLE_OPTIONS", "CHECKBOX", "TEXTBOX_LIST"]
			: ["SINGLE_OPTIONS", "RADIO"],
	};

	const compatibleTypes = compatibilityMap[ccType] || [];
	return compatibleTypes.includes(apType);
}

/**
 * Check if AP field type is compatible with CC field type
 *
 * @param apField - AutoPatient field to check
 * @param ccField - CliniCore field to check against
 * @returns True if types are compatible
 */
function isApToCcTypeCompatible(
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
): boolean {
	const apType = apField.dataType;
	const ccType = ccField.type;
	const allowMultiple = ccField.allowMultipleValues;

	// Direct type mappings
	const compatibilityMap: Record<string, string[]> = {
		TEXT: ["text", "textarea"],
		LARGE_TEXT: ["textarea", "text"],
		NUMERICAL: ["number"],
		PHONE: ["telephone", "text"],
		MONETORY: ["number", "text"],
		EMAIL: ["email", "text"],
		DATE: ["date"],
		CHECKBOX: allowMultiple ? ["select", "select-or-custom"] : [],
		SINGLE_OPTIONS: !allowMultiple ? ["select", "select-or-custom"] : [],
		MULTIPLE_OPTIONS: allowMultiple ? ["select", "select-or-custom"] : [],
		RADIO: ["select", "select-or-custom", "boolean"],
		TEXTBOX_LIST: allowMultiple ? ["select", "select-or-custom"] : [],
		FILE_UPLOAD: [], // No direct CC equivalent
	};

	const compatibleTypes = compatibilityMap[apType] || [];
	return compatibleTypes.includes(ccType);
}

/**
 * Generate unique field name to avoid conflicts
 *
 * Creates a unique field name by appending type and counter suffixes
 * to avoid conflicts with existing fields. This is used when field
 * creation fails due to name conflicts.
 *
 * @param originalName - Original field name that caused conflict
 * @param sourceField - Source field causing the conflict
 * @param sourcePlatform - Platform of the source field
 * @param existingFields - Array of existing fields to check against
 * @param requestId - Request ID for tracing (optional, will generate if not provided)
 * @returns Unique field name that doesn't conflict with existing fields
 *
 * @example
 * ```typescript
 * const uniqueName = generateUniqueFieldName(
 *   "preferences",
 *   apField,
 *   "ap",
 *   existingCcFields,
 *   "req-123"
 * );
 * // Result: "preferences_select" or "preferences_select_2" if needed
 * ```
 *
 * @since 2.0.0
 */
export function generateUniqueFieldName(
	originalName: string,
	sourceField: APGetCustomFieldType | GetCCCustomField,
	sourcePlatform: Platform,
	existingFields: (APGetCustomFieldType | GetCCCustomField)[],
	requestId?: string,
): string {
	const traceId = requestId || getRequestId();
	
	// Generate type suffix based on field type
	let typeSuffix: string;

	if (sourcePlatform === "cc") {
		const ccField = sourceField as GetCCCustomField;
		switch (ccField.type) {
			case "select":
				typeSuffix = ccField.allowMultipleValues ? "multiselect" : "select";
				break;
			case "boolean":
				typeSuffix = "boolean";
				break;
			case "select-or-custom":
				typeSuffix = "selectcustom";
				break;
			default:
				typeSuffix = ccField.type;
		}
	} else {
		const apField = sourceField as APGetCustomFieldType;
		switch (apField.dataType) {
			case "RADIO":
				typeSuffix = "radio";
				break;
			case "MULTIPLE_OPTIONS":
				typeSuffix = "multiselect";
				break;
			case "SINGLE_OPTIONS":
				typeSuffix = "singleselect";
				break;
			default:
				typeSuffix = apField.dataType.toLowerCase();
		}
	}

	// Generate base unique name
	let uniqueName = `${originalName}_${typeSuffix}`;
	let counter = 1;

	// Check if the generated name conflicts with existing fields
	while (findExistingCustomField(uniqueName, existingFields, sourcePlatform === "cc" ? "ap" : "cc")) {
		counter++;
		uniqueName = `${originalName}_${typeSuffix}_${counter}`;
	}

	return uniqueName;
}
