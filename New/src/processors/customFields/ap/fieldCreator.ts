/**
 * AutoPatient Field Creation Utilities
 *
 * Specialized utilities for creating AutoPatient custom fields from
 * CliniCore field definitions. Handles AP-specific field creation
 * logic, validation, and error handling.
 *
 * @fileoverview AP-specific field creation utilities
 * @version 2.0.0
 * @since 2024-07-28
 */

import apiClient from "@apiClient";
import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { logDebug, logError, logInfo, logWarn } from "@/utils/logger";
import { getRequestId } from "@/utils/getRequestId";
import { convertCcFieldToAp } from "../cc/fieldConverter";
import { generateUniqueFieldName } from "../conflict/compatibility";
import { storeMappingForCreatedFields } from "../database/operations";
import type { FieldCreationResult } from "../types";

/**
 * Create AutoPatient field from CliniCore field
 *
 * Creates a new custom field in AutoPatient based on a CliniCore field
 * definition. Handles field conversion, conflict resolution, and database
 * mapping storage with comprehensive error handling.
 *
 * @param ccField - CliniCore field to create in AutoPatient
 * @param requestId - Request ID for tracing and logging correlation (optional, will generate if not provided)
 * @returns Promise resolving to created AP field or null if creation failed
 *
 * @example
 * ```typescript
 * const ccField: GetCCCustomField = {
 *   id: 123,
 *   name: "patient_notes",
 *   label: "Patient Notes",
 *   type: "textarea"
 * };
 *
 * const apField = await createApFieldFromCc(ccField, "req-123");
 * if (apField) {
 *   console.log(`Created AP field: ${apField.name} (ID: ${apField.id})`);
 * }
 * ```
 *
 * @since 2.0.0
 */
export async function createApFieldFromCc(
	ccField: GetCCCustomField,
	requestId?: string,
): Promise<APGetCustomFieldType | null> {
	const traceId = requestId || getRequestId();
	
	try {
		logInfo("Creating AP field from CC field", {
			requestId: traceId,
			ccFieldId: ccField.id,
			ccFieldName: ccField.name,
			ccFieldType: ccField.type,
		});

		// Convert CC field to AP format
		const apFieldData = convertCcFieldToAp(ccField);
		if (!apFieldData) {
			logWarn("CC field conversion returned null, skipping creation", {
				requestId: traceId,
				ccFieldId: ccField.id,
				ccFieldName: ccField.name,
				ccFieldType: ccField.type,
			});
			return null;
		}

		// Attempt to create the field
		const createdField = await apiClient.ap.apCustomfield.create(apFieldData);

		if (createdField) {
			logInfo("Successfully created AP field from CC field", {
				requestId: traceId,
				ccFieldId: ccField.id,
				apFieldId: createdField.id,
				apFieldName: createdField.name,
				apFieldType: createdField.dataType,
			});

			// Store the mapping in database
			await storeMappingForCreatedFields(createdField, ccField, traceId);

			return createdField;
		} else {
			logError("AP field creation returned null", {
				requestId: traceId,
				ccFieldId: ccField.id,
				ccFieldName: ccField.name,
			});
			return null;
		}
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		
		// Check if error is due to existing field conflict
		const isExistingFieldConflict =
			errorMessage.toLowerCase().includes("already exists") ||
			errorMessage.toLowerCase().includes("duplicate") ||
			errorMessage.toLowerCase().includes("conflict") ||
			errorMessage.toLowerCase().includes("name is already taken");

		if (isExistingFieldConflict) {
			logWarn("AP field creation failed due to existing field conflict", {
				requestId: traceId,
				ccFieldId: ccField.id,
				ccFieldName: ccField.name,
				error: errorMessage,
			});

			// Try to find the existing field
			const existingField = await findApFieldByName(ccField.name, traceId);
			if (existingField) {
				logInfo("Found existing AP field, creating mapping", {
					requestId: traceId,
					ccFieldId: ccField.id,
					existingApFieldId: existingField.id,
					existingApFieldName: existingField.name,
				});

				// Store mapping to existing field
				await storeMappingForCreatedFields(existingField, ccField, traceId);
				return existingField;
			}
		}

		logError("Failed to create AP field from CC field", {
			requestId: traceId,
			ccFieldId: ccField.id,
			ccFieldName: ccField.name,
			error: errorMessage,
		});

		return null;
	}
}

/**
 * Create AP field with conflict resolution
 *
 * Enhanced field creation function that handles name conflicts by
 * generating unique field names when necessary. Provides comprehensive
 * result tracking and error handling.
 *
 * @param ccField - CliniCore field to create in AutoPatient
 * @param existingApFields - Existing AP fields to check for conflicts
 * @param requestId - Request ID for tracing and logging correlation (optional, will generate if not provided)
 * @returns Promise resolving to comprehensive creation result
 *
 * @example
 * ```typescript
 * const result = await createApFieldWithConflictResolution(
 *   ccField,
 *   existingApFields,
 *   "req-123"
 * );
 *
 * if (result.success && result.field) {
 *   console.log(`Successfully created field: ${result.field.name}`);
 * } else {
 *   console.error(`Creation failed: ${result.error}`);
 * }
 * ```
 *
 * @since 2.0.0
 */
export async function createApFieldWithConflictResolution(
	ccField: GetCCCustomField,
	existingApFields: APGetCustomFieldType[],
	requestId?: string,
): Promise<FieldCreationResult> {
	const traceId = requestId || getRequestId();
	
	try {
		// First try normal creation
		const createdField = await createApFieldFromCc(ccField, traceId);
		
		if (createdField) {
			return {
				success: true,
				field: createdField,
			};
		}

		// If creation failed, try with unique name
		const uniqueName = generateUniqueFieldName(
			ccField.name,
			ccField,
			"cc",
			existingApFields,
			traceId,
		);

		logInfo("Retrying AP field creation with unique name", {
			requestId: traceId,
			ccFieldId: ccField.id,
			originalName: ccField.name,
			uniqueName,
		});

		// Create modified CC field with unique name
		const modifiedCcField: GetCCCustomField = {
			...ccField,
			name: uniqueName,
			label: uniqueName,
		};

		const retryCreatedField = await createApFieldFromCc(modifiedCcField, traceId);
		
		if (retryCreatedField) {
			return {
				success: true,
				field: retryCreatedField,
			};
		} else {
			return {
				success: false,
				error: "Field creation failed even with unique name",
			};
		}
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		const isExistingFieldConflict =
			errorMessage.toLowerCase().includes("already exists") ||
			errorMessage.toLowerCase().includes("duplicate") ||
			errorMessage.toLowerCase().includes("conflict");

		return {
			success: false,
			error: errorMessage,
			existingFieldConflict: isExistingFieldConflict,
		};
	}
}

/**
 * Find existing AP field by name
 *
 * Searches through all AP custom fields to find one with the specified name.
 * Used for conflict resolution and field mapping operations.
 *
 * @param fieldName - Name of the field to search for
 * @param requestId - Request ID for tracing and logging correlation (optional, will generate if not provided)
 * @returns Promise resolving to the existing AP field or null if not found
 *
 * @example
 * ```typescript
 * const existingField = await findApFieldByName("patient_notes", "req-123");
 * if (existingField) {
 *   console.log(`Found existing field: ${existingField.name} (ID: ${existingField.id})`);
 * }
 * ```
 *
 * @since 2.0.0
 */
export async function findApFieldByName(
	fieldName: string,
	requestId?: string,
): Promise<APGetCustomFieldType | null> {
	const traceId = requestId || getRequestId();
	
	try {
		logDebug("Searching for AP field by name", {
			requestId: traceId,
			fieldName,
		});

		// Fetch all AP fields to search through them
		const allApFields = await apiClient.ap.apCustomfield.allWithParentFilter(true); // Invalidate cache

		// Find field with matching name or fieldKey
		const existingField = allApFields.find(field => 
			field.name === fieldName || field.fieldKey === fieldName
		);

		if (existingField) {
			logDebug("Found existing AP field with matching name", {
				requestId: traceId,
				fieldName,
				existingFieldId: existingField.id,
				existingFieldName: existingField.name,
				existingFieldType: existingField.dataType,
			});
			return existingField;
		}

		logDebug("No existing AP field found with matching name", {
			requestId: traceId,
			fieldName,
			totalApFields: allApFields.length,
		});

		return null;
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logError("Failed to search for AP field by name", {
			requestId: traceId,
			fieldName,
			error: errorMessage,
		});
		return null;
	}
}
