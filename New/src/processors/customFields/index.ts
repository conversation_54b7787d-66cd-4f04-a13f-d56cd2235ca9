/**
 * Custom Fields Processing Module
 *
 * Comprehensive custom field processing utilities for AutoPatient and CliniCore platforms.
 * Provides complete field synchronization, matching, creation, conflict detection, and
 * database operations with intelligent algorithms and comprehensive error handling.
 *
 * **Organized Structure:**
 * - `ap/`: AutoPatient-specific field utilities (conversion, creation, validation)
 * - `cc/`: CliniCore-specific field utilities (conversion, creation, validation)
 * - `conflict/`: Conflict detection and resolution utilities
 * - `database/`: Database operations and mapping storage
 * - `matching/`: Field matching strategies and algorithms
 * - `synchronization/`: Main synchronization engine and coordination
 * - `config/`: Configuration utilities (type mappings, normalization, etc.)
 *
 * **Key Features:**
 * - Comprehensive bidirectional field synchronization
 * - Intelligent field matching with multiple strategies
 * - Automatic field creation with conflict detection
 * - Standard field mapping and blocklist filtering
 * - Database operations with upsert logic and error handling
 * - Detailed logging and statistics tracking with requestId tracing
 * - Strict TypeScript compliance without `any` usage
 * - Performance-optimized for production environments
 * - Modular architecture for maintainability and testing
 *
 * @example
 * ```typescript
 * import {
 *   synchronizeCustomFields,
 *   fieldsMatch,
 *   createApFieldFromCc,
 *   detectFieldConflicts,
 *   storeMappingForCreatedFields
 * } from '@processors/customFields';
 *
 * // Complete field synchronization
 * const syncResult = await synchronizeCustomFields("req-123");
 * console.log(`Synchronized ${syncResult.matchedCount} field pairs`);
 *
 * // Individual field matching
 * const isMatch = fieldsMatch(apField, ccField);
 *
 * // Field creation with conflict detection
 * const conflicts = detectFieldConflicts(sourceField, targetFields, "ap", "cc", "req-123");
 * if (!conflicts.hasConflict) {
 *   const createdField = await createApFieldFromCc(ccField, "req-123");
 * }
 * ```
 *
 * @since 1.0.0
 * @version 3.0.0
 */

// Platform-specific utilities
export * from "./ap";
export * from "./cc";

// Core functionality modules
export * from "./conflict";
export * from "./database";
export * from "./matching";
export * from "./synchronization";

// Configuration utilities
export * from "./config/fieldTypeMappings";
export * from "./config/fieldNormalization";
export * from "./config/textboxListHandling";

// Legacy compatibility exports
export { default as apToCcCustomFieldConvert } from "./apToCcCustomFieldConvert";
export { default as ccToApCustomFieldConvert } from "./ccToApCustomFieldConvert";

// Backward compatibility aliases
export { createApFieldFromCc as createApFieldInCc } from "./ap";
export { createCcFieldFromAp as createCcFieldInAp } from "./cc";

// Type definitions
export type {
	CustomFieldInsert,
	CustomFieldSyncResponse,
	FieldConflictResult,
	FieldCreationResult,
	Platform,
	FieldMatchResult,
	CustomFieldMapping,
} from "./types";
export { FieldMatchStrategy } from "./types";
