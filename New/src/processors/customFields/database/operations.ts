/**
 * Custom Fields Database Operations
 *
 * Provides database operations for custom field synchronization between
 * AutoPatient (AP) and CliniCore (CC) platforms. Handles field mapping
 * storage, updates, and upsert operations with comprehensive error handling
 * and logging for data integrity and traceability.
 *
 * @fileoverview Database operations for custom field synchronization
 * @version 2.0.0
 * @since 2024-07-28
 */

import { dbSchema, getDb } from "@database";
import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { eq } from "drizzle-orm";
import type { StandardFieldMapping } from "@/config/standardFieldMappings";
import { logDebug, logError } from "@/utils/logger";
import { getRequestId } from "@/utils/getRequestId";
import type { CustomFieldInsert } from "../types";

/**
 * Store mapping for newly created field pairs
 *
 * Creates a database record linking an AutoPatient custom field with its
 * corresponding CliniCore custom field. This mapping enables bidirectional
 * synchronization and prevents duplicate field creation in future operations.
 *
 * The function uses upsert logic to handle cases where a mapping might
 * already exist, ensuring data consistency and preventing constraint violations.
 *
 * @param apField - AutoPatient custom field that was created or matched
 * @param ccField - CliniCore custom field that was created or matched
 * @param requestId - Request ID for tracing and logging correlation (optional, will generate if not provided)
 * @returns Promise that resolves when mapping is successfully stored
 *
 * @throws {Error} When database operation fails or field data is invalid
 *
 * @example
 * ```typescript
 * const apField: APGetCustomFieldType = {
 *   id: 123,
 *   name: "patient_notes",
 *   dataType: "TEXTAREA"
 * };
 *
 * const ccField: GetCCCustomField = {
 *   id: 456,
 *   name: "patient_notes",
 *   label: "Patient Notes",
 *   type: "TEXTAREA"
 * };
 *
 * await storeMappingForCreatedFields(apField, ccField, "req-123");
 * console.log("Field mapping stored successfully");
 * ```
 *
 * @since 2.0.0
 */
export async function storeMappingForCreatedFields(
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId?: string,
): Promise<void> {
	const traceId = requestId || getRequestId();
	
	try {
		const db = getDb();
		const mappingData: CustomFieldInsert = {
			apId: apField.id,
			ccId: ccField.id,
			name: apField.name,
			label: ccField.label,
			type: ccField.type,
			apConfig: apField,
			ccConfig: ccField,
			mappingType: "custom_to_custom",
			apStandardField: null,
			ccStandardField: null,
		};

		await db
			.insert(dbSchema.customFields)
			.values(mappingData)
			.onConflictDoUpdate({
				target: [dbSchema.customFields.apId],
				set: {
					ccId: mappingData.ccId,
					name: mappingData.name,
					label: mappingData.label,
					type: mappingData.type,
					apConfig: mappingData.apConfig,
					ccConfig: mappingData.ccConfig,
					mappingType: mappingData.mappingType,
					apStandardField: mappingData.apStandardField,
					ccStandardField: mappingData.ccStandardField,
					updatedAt: new Date(),
				},
			});

		logDebug("Stored mapping for newly created field pair", {
			requestId: traceId,
			apFieldId: apField.id,
			ccFieldId: ccField.id,
		});
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logError("Failed to store field mapping", {
			requestId: traceId,
			apFieldId: apField.id,
			ccFieldId: ccField.id,
			error: errorMessage,
		});
		throw new Error(`Failed to store field mapping: ${errorMessage}`);
	}
}

/**
 * Store standard field mapping
 *
 * Creates a database record for a custom field that maps to a standard field
 * on the target platform. This prevents future attempts to create duplicate
 * custom fields for standard field mappings.
 *
 * @param customField - Custom field that maps to a standard field
 * @param standardFieldMapping - Standard field mapping configuration
 * @param requestId - Request ID for tracing and logging correlation (optional, will generate if not provided)
 * @returns Promise that resolves when mapping is successfully stored
 *
 * @throws {Error} When database operation fails or mapping data is invalid
 *
 * @example
 * ```typescript
 * const mapping: StandardFieldMapping = {
 *   sourceField: "email_address",
 *   targetField: "email",
 *   sourcePlatform: "ap",
 *   targetPlatform: "cc"
 * };
 *
 * await storeStandardFieldMapping(apField, mapping, "req-123");
 * console.log("Standard field mapping stored");
 * ```
 *
 * @since 2.0.0
 */
export async function storeStandardFieldMapping(
	customField: APGetCustomFieldType | GetCCCustomField,
	standardFieldMapping: StandardFieldMapping,
	requestId?: string,
): Promise<void> {
	const traceId = requestId || getRequestId();
	
	try {
		const db = getDb();
		const isApCustomField = "dataType" in customField;

		const mappingData: CustomFieldInsert = {
			apId: isApCustomField ? customField.id : null,
			ccId: !isApCustomField ? customField.id : null,
			name: customField.name,
			label: isApCustomField
				? customField.name
				: (customField as GetCCCustomField).label,
			type: isApCustomField
				? customField.dataType
				: (customField as GetCCCustomField).type,
			apConfig: isApCustomField ? customField : null,
			ccConfig: !isApCustomField ? customField : null,
			mappingType:
				standardFieldMapping.sourcePlatform === "ap"
					? "custom_to_standard"
					: "standard_to_custom",
			apStandardField:
				standardFieldMapping.targetPlatform === "ap"
					? standardFieldMapping.targetField
					: null,
			ccStandardField:
				standardFieldMapping.targetPlatform === "cc"
					? standardFieldMapping.targetField
					: null,
		};

		await db
			.insert(dbSchema.customFields)
			.values(mappingData)
			.onConflictDoUpdate({
				target: isApCustomField
					? [dbSchema.customFields.apId]
					: [dbSchema.customFields.ccId],
				set: {
					apId: mappingData.apId,
					ccId: mappingData.ccId,
					name: mappingData.name,
					label: mappingData.label,
					type: mappingData.type,
					apConfig: mappingData.apConfig,
					ccConfig: mappingData.ccConfig,
					mappingType: mappingData.mappingType,
					apStandardField: mappingData.apStandardField,
					ccStandardField: mappingData.ccStandardField,
					updatedAt: new Date(),
				},
			});

		logDebug("Stored standard field mapping", {
			requestId: traceId,
			customFieldId: customField.id,
			customFieldName: customField.name,
			standardField: standardFieldMapping.targetField,
			mappingType: mappingData.mappingType,
		});
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logError("Failed to store standard field mapping", {
			requestId: traceId,
			customFieldId: customField.id,
			standardField: standardFieldMapping.targetField,
			error: errorMessage,
		});
		throw new Error(`Failed to store standard field mapping: ${errorMessage}`);
	}
}

/**
 * Update existing field mapping
 *
 * Updates an existing field mapping record with new information.
 * Used when field configurations change or additional mapping
 * information becomes available.
 *
 * @param mappingId - ID of the mapping record to update
 * @param updateData - Partial mapping data to update
 * @param requestId - Request ID for tracing and logging correlation (optional, will generate if not provided)
 * @returns Promise that resolves when mapping is successfully updated
 *
 * @throws {Error} When database operation fails or mapping not found
 *
 * @example
 * ```typescript
 * await updateFieldMapping("mapping-123", {
 *   label: "Updated Label",
 *   type: "textarea"
 * }, "req-123");
 * ```
 *
 * @since 2.0.0
 */
export async function updateFieldMapping(
	mappingId: string,
	updateData: Partial<CustomFieldInsert>,
	requestId?: string,
): Promise<void> {
	const traceId = requestId || getRequestId();
	
	try {
		const db = getDb();

		await db
			.update(dbSchema.customFields)
			.set({
				...updateData,
				updatedAt: new Date(),
			})
			.where(eq(dbSchema.customFields.id, mappingId));

		logDebug("Updated field mapping", {
			requestId: traceId,
			mappingId,
			updateFields: Object.keys(updateData),
		});
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logError("Failed to update field mapping", {
			requestId: traceId,
			mappingId,
			error: errorMessage,
		});
		throw new Error(`Failed to update field mapping: ${errorMessage}`);
	}
}

/**
 * Bulk upsert field mappings for improved performance
 *
 * Performs bulk upsert operations for multiple field mappings to improve
 * performance when processing large numbers of field relationships.
 * This function is optimized for batch operations during full synchronization.
 *
 * @param mappings - Array of field mapping data to upsert
 * @param requestId - Request ID for tracing and logging correlation (optional, will generate if not provided)
 * @returns Promise that resolves when all mappings are successfully processed
 *
 * @throws {Error} When bulk operation fails or data validation errors occur
 *
 * @example
 * ```typescript
 * const mappings: CustomFieldInsert[] = [
 *   { apId: "1", ccId: 101, name: "field1", ... },
 *   { apId: "2", ccId: 102, name: "field2", ... }
 * ];
 *
 * await bulkUpsertFieldMappings(mappings, "req-123");
 * console.log("Bulk mappings completed");
 * ```
 *
 * @since 2.0.0
 */
export async function bulkUpsertFieldMappings(
	mappings: CustomFieldInsert[],
	requestId?: string,
): Promise<void> {
	const traceId = requestId || getRequestId();

	if (mappings.length === 0) {
		logDebug("No mappings to process in bulk operation", {
			requestId: traceId,
		});
		return;
	}

	try {
		const db = getDb();

		// Process mappings in batches to avoid overwhelming the database
		const batchSize = 50;
		const batches = [];

		for (let i = 0; i < mappings.length; i += batchSize) {
			batches.push(mappings.slice(i, i + batchSize));
		}

		for (const batch of batches) {
			await db
				.insert(dbSchema.customFields)
				.values(batch)
				.onConflictDoUpdate({
					target: [dbSchema.customFields.apId],
					set: {
						ccId: dbSchema.customFields.ccId,
						name: dbSchema.customFields.name,
						label: dbSchema.customFields.label,
						type: dbSchema.customFields.type,
						apConfig: dbSchema.customFields.apConfig,
						ccConfig: dbSchema.customFields.ccConfig,
						mappingType: dbSchema.customFields.mappingType,
						apStandardField: dbSchema.customFields.apStandardField,
						ccStandardField: dbSchema.customFields.ccStandardField,
						updatedAt: new Date(),
					},
				});
		}

		logDebug("Completed bulk upsert of field mappings", {
			requestId: traceId,
			totalMappings: mappings.length,
			batchCount: batches.length,
		});
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logError("Failed to bulk upsert field mappings", {
			requestId: traceId,
			mappingCount: mappings.length,
			error: errorMessage,
		});
		throw new Error(`Failed to bulk upsert field mappings: ${errorMessage}`);
	}
}
