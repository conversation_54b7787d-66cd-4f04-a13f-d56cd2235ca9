/**
 * Custom Fields Database Operations Module
 *
 * Comprehensive database operations for custom field synchronization
 * between AutoPatient (AP) and CliniCore (CC) platforms. Provides
 * field mapping storage, updates, bulk operations, and query utilities
 * with comprehensive error handling and logging.
 *
 * @fileoverview Database operations module exports
 * @version 2.0.0
 * @since 2024-07-28
 */

// Core database operations
export {
	storeMappingForCreatedFields,
	storeStandardFieldMapping,
	updateFieldMapping,
	bulkUpsertFieldMappings,
} from "./operations";

// Re-export types for convenience
export type { CustomFieldInsert, CustomFieldMapping } from "../types";
