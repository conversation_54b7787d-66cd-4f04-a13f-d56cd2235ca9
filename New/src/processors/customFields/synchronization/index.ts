/**
 * Custom Fields Synchronization Module
 *
 * Main synchronization module for custom field synchronization between
 * AutoPatient (AP) and CliniCore (CC) platforms. Provides the core
 * synchronization engine and coordination utilities.
 *
 * @fileoverview Synchronization module exports
 * @version 2.0.0
 * @since 2024-07-28
 */

// Core synchronization engine
export { synchronizeCustomFields } from "./engine";

// Re-export types for convenience
export type { CustomFieldSyncResponse } from "../types";
