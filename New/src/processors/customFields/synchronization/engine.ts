/**
 * Custom Fields Synchronization Engine
 *
 * Main synchronization engine for custom field synchronization between
 * AutoPatient (AP) and CliniCore (CC) platforms. Orchestrates the complete
 * synchronization process including field matching, creation, and mapping.
 *
 * @fileoverview Main synchronization engine for custom fields
 * @version 2.0.0
 * @since 2024-07-28
 */

import apiClient from "@apiClient";
import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { logDebug, logError, logInfo } from "@/utils/logger";
import { getRequestId } from "@/utils/getRequestId";
import { matchAPFieldWithCC, matchCCFieldWithAP } from "../matching/algorithms";
import { detectFieldConflicts } from "../conflict/detector";
import { createApFieldFromCc } from "../ap/fieldCreator";
import { createCcFieldFromAp } from "../cc/fieldCreator";
import { storeMappingForCreatedFields, storeStandardFieldMapping } from "../database/operations";
import { getDb, dbSchema } from "@database";
import type { CustomFieldSyncResponse, CustomFieldMapping } from "../types";

/**
 * Synchronize custom fields between platforms
 *
 * Performs comprehensive bidirectional synchronization of custom fields
 * between AutoPatient and CliniCore platforms. Includes field matching,
 * conflict detection, field creation, and database mapping storage.
 *
 * @param requestId - Request ID for tracing and logging correlation (optional, will generate if not provided)
 * @returns Promise resolving to synchronization results and statistics
 *
 * @example
 * ```typescript
 * const syncResult = await synchronizeCustomFields("req-123");
 * console.log(`Synchronized ${syncResult.matchedCount} field pairs`);
 * console.log(`Created ${syncResult.createdCount} new fields`);
 * ```
 *
 * @since 2.0.0
 */
export async function synchronizeCustomFields(
	requestId?: string,
): Promise<CustomFieldSyncResponse> {
	const traceId = requestId || getRequestId();
	const startTime = Date.now();
	
	logInfo("Starting custom field synchronization", {
		requestId: traceId,
	});

	const result: CustomFieldSyncResponse = {
		success: false,
		matchedCount: 0,
		createdCount: 0,
		skippedCount: 0,
		errorCount: 0,
		warnings: [],
		errors: [],
		processingTimeMs: 0,
	};

	try {
		// Fetch all custom fields from both platforms
		const [apFields, ccFields, existingMappings] = await Promise.all([
			apiClient.ap.apCustomfield.allWithParentFilter(true),
			apiClient.cc.ccCustomfield.allWithParentFilter(true),
			getExistingMappings(traceId),
		]);

		logInfo("Fetched custom fields from both platforms", {
			requestId: traceId,
			apFieldCount: apFields.length,
			ccFieldCount: ccFields.length,
			existingMappingCount: existingMappings.length,
		});

		// Phase 1: Match existing fields
		const matchResults = await matchExistingFields(
			apFields,
			ccFields,
			existingMappings,
			traceId,
		);

		result.matchedCount = matchResults.matchedCount;
		result.warnings.push(...matchResults.warnings);

		// Phase 2: Create missing fields (AP to CC)
		const apToCcResults = await createMissingFields(
			apFields,
			ccFields,
			existingMappings,
			"ap",
			traceId,
		);

		result.createdCount += apToCcResults.createdCount;
		result.skippedCount += apToCcResults.skippedCount;
		result.errorCount += apToCcResults.errorCount;
		result.warnings.push(...apToCcResults.warnings);
		result.errors.push(...apToCcResults.errors);

		// Phase 3: Create missing fields (CC to AP)
		const ccToApResults = await createMissingFields(
			ccFields,
			apFields,
			existingMappings,
			"cc",
			traceId,
		);

		result.createdCount += ccToApResults.createdCount;
		result.skippedCount += ccToApResults.skippedCount;
		result.errorCount += ccToApResults.errorCount;
		result.warnings.push(...ccToApResults.warnings);
		result.errors.push(...ccToApResults.errors);

		result.success = result.errorCount === 0;
		result.processingTimeMs = Date.now() - startTime;

		logInfo("Completed custom field synchronization", {
			requestId: traceId,
			success: result.success,
			matchedCount: result.matchedCount,
			createdCount: result.createdCount,
			skippedCount: result.skippedCount,
			errorCount: result.errorCount,
			processingTimeMs: result.processingTimeMs,
		});

		return result;
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		result.success = false;
		result.errorCount++;
		result.errors.push(errorMessage);
		result.processingTimeMs = Date.now() - startTime;

		logError("Custom field synchronization failed", {
			requestId: traceId,
			error: errorMessage,
			processingTimeMs: result.processingTimeMs,
		});

		return result;
	}
}

/**
 * Get existing field mappings from database
 */
async function getExistingMappings(requestId: string): Promise<CustomFieldMapping[]> {
	try {
		const db = getDb();
		const mappings = await db.select().from(dbSchema.customFields);
		
		logDebug("Retrieved existing field mappings", {
			requestId,
			mappingCount: mappings.length,
		});

		return mappings;
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logError("Failed to retrieve existing mappings", {
			requestId,
			error: errorMessage,
		});
		return [];
	}
}

/**
 * Match existing fields between platforms
 */
async function matchExistingFields(
	apFields: APGetCustomFieldType[],
	ccFields: GetCCCustomField[],
	existingMappings: CustomFieldMapping[],
	requestId: string,
): Promise<{
	matchedCount: number;
	warnings: string[];
}> {
	const result = {
		matchedCount: 0,
		warnings: [] as string[],
	};

	// Filter out already mapped fields
	const mappedApIds = new Set(existingMappings.map(m => m.apId).filter(Boolean));
	const mappedCcIds = new Set(existingMappings.map(m => m.ccId).filter(Boolean));

	const unmappedApFields = apFields.filter(field => !mappedApIds.has(field.id));
	const unmappedCcFields = ccFields.filter(field => !mappedCcIds.has(field.id));

	logDebug("Matching unmapped fields", {
		requestId,
		unmappedApFields: unmappedApFields.length,
		unmappedCcFields: unmappedCcFields.length,
	});

	// Match AP fields with CC fields
	for (const apField of unmappedApFields) {
		const matchResult = matchAPFieldWithCC(
			apField,
			unmappedCcFields,
			existingMappings,
			requestId,
		);

		if (matchResult.matched && matchResult.ccField) {
			try {
				await storeMappingForCreatedFields(apField, matchResult.ccField, requestId);
				result.matchedCount++;
				
				// Remove matched CC field from available list
				const ccIndex = unmappedCcFields.findIndex(f => f.id === matchResult.ccField!.id);
				if (ccIndex !== -1) {
					unmappedCcFields.splice(ccIndex, 1);
				}
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : String(error);
				result.warnings.push(`Failed to store mapping for ${apField.name}: ${errorMessage}`);
			}
		}
	}

	return result;
}

/**
 * Create missing fields on target platform
 */
async function createMissingFields(
	sourceFields: (APGetCustomFieldType | GetCCCustomField)[],
	targetFields: (APGetCustomFieldType | GetCCCustomField)[],
	existingMappings: CustomFieldMapping[],
	sourcePlatform: "ap" | "cc",
	requestId: string,
): Promise<{
	createdCount: number;
	skippedCount: number;
	errorCount: number;
	warnings: string[];
	errors: string[];
}> {
	const result = {
		createdCount: 0,
		skippedCount: 0,
		errorCount: 0,
		warnings: [] as string[],
		errors: [] as string[],
	};

	const targetPlatform = sourcePlatform === "ap" ? "cc" : "ap";
	
	// Get mapped source field IDs
	const mappedSourceIds = new Set(
		existingMappings
			.map(m => sourcePlatform === "ap" ? m.apId : m.ccId)
			.filter(Boolean)
	);

	// Filter unmapped source fields
	const unmappedSourceFields = sourceFields.filter(field => 
		!mappedSourceIds.has(field.id)
	);

	logDebug("Creating missing fields", {
		requestId,
		sourcePlatform,
		targetPlatform,
		unmappedSourceFields: unmappedSourceFields.length,
	});

	for (const sourceField of unmappedSourceFields) {
		try {
			// Check for conflicts
			const conflictResult = detectFieldConflicts(
				sourceField,
				targetFields,
				sourcePlatform,
				targetPlatform,
				requestId,
			);

			if (conflictResult.hasConflict) {
				if (conflictResult.conflictType === "standard_field" && conflictResult.standardMapping) {
					// Store standard field mapping
					await storeStandardFieldMapping(
						sourceField,
						conflictResult.standardMapping,
						requestId,
					);
					result.skippedCount++;
				} else if (conflictResult.conflictType === "existing_custom_field" && conflictResult.existingField) {
					// Store mapping to existing field
					if (sourcePlatform === "ap") {
						await storeMappingForCreatedFields(
							sourceField as APGetCustomFieldType,
							conflictResult.existingField as GetCCCustomField,
							requestId,
						);
					} else {
						await storeMappingForCreatedFields(
							conflictResult.existingField as APGetCustomFieldType,
							sourceField as GetCCCustomField,
							requestId,
						);
					}
					result.skippedCount++;
				} else {
					// Blocklist or other conflict
					result.skippedCount++;
				}
				continue;
			}

			// Create field on target platform
			let createdField: APGetCustomFieldType | GetCCCustomField | null = null;

			if (sourcePlatform === "ap" && targetPlatform === "cc") {
				createdField = await createCcFieldFromAp(
					sourceField as APGetCustomFieldType,
					requestId,
				);
			} else if (sourcePlatform === "cc" && targetPlatform === "ap") {
				createdField = await createApFieldFromCc(
					sourceField as GetCCCustomField,
					requestId,
				);
			}

			if (createdField) {
				result.createdCount++;
			} else {
				result.errorCount++;
				result.errors.push(`Failed to create field: ${sourceField.name}`);
			}
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : String(error);
			result.errorCount++;
			result.errors.push(`Error processing field ${sourceField.name}: ${errorMessage}`);
		}
	}

	return result;
}
