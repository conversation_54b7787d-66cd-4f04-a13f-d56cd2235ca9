/**
 * Field Matching Strategies
 *
 * Intelligent field matching between AutoPatient and CliniCore custom fields
 * using normalization, similarity scoring, and conflict detection. Provides
 * multiple matching strategies for different use cases.
 *
 * @fileoverview Custom field matching strategies and utilities
 * @version 2.0.0
 * @since 2024-07-28
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { getRequestId } from "@/utils/getRequestId";
import { logDebug, logInfo, logWarn } from "@/utils/logger";
import {
	normalizeFieldName,
	normalizedFieldsMatch,
	areFieldNameVariations,
	calculateFieldNameSimilarity
} from "../config/fieldNormalization";
import {
	findMatchingTextboxListField,
	areTextboxListFieldsCompatible
} from "../config/textboxListHandling";
import type { Platform, FieldMatchResult, CustomFieldMapping } from "../types";

/**
 * Find existing custom field by name
 *
 * Searches for an existing custom field by name on the specified platform.
 * Used for conflict detection and field mapping operations. Supports both
 * exact name matching and platform-specific field key matching.
 *
 * @param fieldName - Name of the field to search for
 * @param fields - Array of custom fields to search through
 * @param platform - Platform identifier ("ap" | "cc")
 * @returns Existing field if found, undefined otherwise
 *
 * @example
 * ```typescript
 * const existingField = findExistingCustomField(
 *   "patient_notes",
 *   ccCustomFields,
 *   "cc"
 * );
 *
 * if (existingField) {
 *   console.log(`Found existing field: ${existingField.name}`);
 * }
 * ```
 *
 * @since 2.0.0
 */
export function findExistingCustomField(
	fieldName: string,
	fields: (APGetCustomFieldType | GetCCCustomField)[],
	platform: Platform,
): APGetCustomFieldType | GetCCCustomField | undefined {
	return fields.find(field => {
		if (platform === "ap") {
			// For AP fields, check name and fieldKey
			const apField = field as APGetCustomFieldType;
			return apField.name === fieldName || apField.fieldKey === fieldName;
		} else {
			// For CC fields, check name and label
			const ccField = field as GetCCCustomField;
			return ccField.name === fieldName || ccField.label === fieldName;
		}
	});
}

/**
 * Check if two fields match based on name and type compatibility
 *
 * Determines if two fields from different platforms should be considered
 * a match for synchronization purposes. Uses comprehensive name matching
 * and basic type compatibility checking.
 *
 * @param field1 - First field to compare
 * @param field2 - Second field to compare
 * @returns True if fields match, false otherwise
 *
 * @example
 * ```typescript
 * const isMatch = fieldsMatch(apField, ccField);
 * if (isMatch) {
 *   console.log("Fields are compatible for synchronization");
 * }
 * ```
 *
 * @since 2.0.0
 */
export function fieldsMatch(
	field1: APGetCustomFieldType | GetCCCustomField,
	field2: APGetCustomFieldType | GetCCCustomField,
): boolean {
	// Check if one is AP and one is CC
	const isField1AP = "dataType" in field1;
	const isField2AP = "dataType" in field2;
	
	if (isField1AP === isField2AP) {
		// Both are from same platform, not a valid comparison
		return false;
	}
	
	const apField = isField1AP ? field1 as APGetCustomFieldType : field2 as APGetCustomFieldType;
	const ccField = isField1AP ? field2 as GetCCCustomField : field1 as GetCCCustomField;
	
	// Check name match
	const nameMatch = apField.name === ccField.name || 
					  apField.name === ccField.label ||
					  (apField.fieldKey && (apField.fieldKey === ccField.name || apField.fieldKey === ccField.label));
	
	if (!nameMatch) {
		return false;
	}
	
	// Check type compatibility (simplified check)
	// This could be enhanced with the full type compatibility logic
	return true;
}

/**
 * Find matching field using comprehensive matching strategies
 *
 * Uses multiple matching strategies to find the best match for a field
 * across platforms. Tries exact matching first, then normalized matching,
 * and finally similarity-based matching.
 *
 * @param sourceField - Field to find a match for
 * @param targetFields - Array of fields to search through
 * @param platform - Target platform ("ap" | "cc")
 * @param requestId - Request ID for tracing (optional, will generate if not provided)
 * @returns Matching field if found, undefined otherwise
 *
 * @example
 * ```typescript
 * const matchingField = findMatchingField(
 *   apField,
 *   ccCustomFields,
 *   "cc",
 *   "req-123"
 * );
 *
 * if (matchingField) {
 *   console.log(`Found match: ${matchingField.name}`);
 * }
 * ```
 *
 * @since 2.0.0
 */
export function findMatchingField(
	sourceField: APGetCustomFieldType | GetCCCustomField,
	targetFields: (APGetCustomFieldType | GetCCCustomField)[],
	platform: Platform,
	requestId?: string,
): APGetCustomFieldType | GetCCCustomField | undefined {
	const traceId = requestId || getRequestId();
	
	// First try exact name match
	const exactMatch = findExistingCustomField(sourceField.name, targetFields, platform);
	if (exactMatch) {
		logDebug("Found exact name match", {
			requestId: traceId,
			sourceFieldName: sourceField.name,
			matchedFieldName: exactMatch.name,
			platform,
		});
		return exactMatch;
	}
	
	// Try normalized name matching
	const normalizedSourceName = normalizeFieldName(sourceField.name);
	for (const targetField of targetFields) {
		const normalizedTargetName = normalizeFieldName(targetField.name);
		if (normalizedSourceName === normalizedTargetName) {
			logDebug("Found normalized name match", {
				requestId: traceId,
				sourceFieldName: sourceField.name,
				matchedFieldName: targetField.name,
				normalizedName: normalizedSourceName,
				platform,
			});
			return targetField;
		}
		
		// For CC fields, also check label
		if (platform === "cc" && "label" in targetField) {
			const normalizedLabel = normalizeFieldName((targetField as GetCCCustomField).label);
			if (normalizedSourceName === normalizedLabel) {
				logDebug("Found normalized label match", {
					requestId: traceId,
					sourceFieldName: sourceField.name,
					matchedFieldLabel: (targetField as GetCCCustomField).label,
					normalizedName: normalizedSourceName,
					platform,
				});
				return targetField;
			}
		}
	}
	
	// Try similarity-based matching with high threshold
	const similarityThreshold = 0.85;
	let bestMatch: { field: APGetCustomFieldType | GetCCCustomField; similarity: number } | undefined;
	
	for (const targetField of targetFields) {
		const nameSimilarity = calculateFieldNameSimilarity(sourceField.name, targetField.name);
		
		if (nameSimilarity >= similarityThreshold) {
			if (!bestMatch || nameSimilarity > bestMatch.similarity) {
				bestMatch = { field: targetField, similarity: nameSimilarity };
			}
		}
		
		// For CC fields, also check label similarity
		if (platform === "cc" && "label" in targetField) {
			const labelSimilarity = calculateFieldNameSimilarity(
				sourceField.name, 
				(targetField as GetCCCustomField).label
			);
			
			if (labelSimilarity >= similarityThreshold) {
				if (!bestMatch || labelSimilarity > bestMatch.similarity) {
					bestMatch = { field: targetField, similarity: labelSimilarity };
				}
			}
		}
	}
	
	if (bestMatch) {
		logDebug("Found similarity-based match", {
			requestId: traceId,
			sourceFieldName: sourceField.name,
			matchedFieldName: bestMatch.field.name,
			similarity: bestMatch.similarity,
			platform,
		});
		return bestMatch.field;
	}
	
	logDebug("No matching field found", {
		requestId: traceId,
		sourceFieldName: sourceField.name,
		targetFieldCount: targetFields.length,
		platform,
	});
	
	return undefined;
}
